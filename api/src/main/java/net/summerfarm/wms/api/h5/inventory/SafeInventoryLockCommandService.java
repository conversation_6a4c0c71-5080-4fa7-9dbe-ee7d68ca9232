package net.summerfarm.wms.api.h5.inventory;

import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockCreateCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockOperationCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockUpdateCommand;

/**
 * <AUTHOR>
 * @description 安全库存锁定命令服务
 * @date 2025/07/30
 */
public interface SafeInventoryLockCommandService {

    /**
     * 创建安全库存锁定记录
     *
     * @param createCommand 创建命令
     * @return 主键ID
     */
    Long createSafeInventoryLock(SafeInventoryLockCreateCommand createCommand);

    /**
     * 更新安全库存锁定记录
     *
     * @param updateCommand 更新命令
     * @return 是否成功
     */
    Boolean updateSafeInventoryLock(SafeInventoryLockUpdateCommand updateCommand);

    /**
     * 删除安全库存锁定记录
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteSafeInventoryLock(Long id);

    /**
     * 锁定库存
     *
     * @param operationCommand 操作命令
     * @return 是否成功
     */
    Boolean lockInventory(SafeInventoryLockOperationCommand operationCommand);

    /**
     * 释放锁定
     *
     * @param operationCommand 操作命令
     * @return 是否成功
     */
    Boolean releaseLock(SafeInventoryLockOperationCommand operationCommand);
}
