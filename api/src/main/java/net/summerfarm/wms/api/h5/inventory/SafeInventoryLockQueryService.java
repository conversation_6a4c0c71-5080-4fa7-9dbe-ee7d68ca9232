package net.summerfarm.wms.api.h5.inventory;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockQuery;
import net.summerfarm.wms.api.h5.inventory.dto.res.SafeInventoryLockRes;

import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定查询服务
 * @date 2025/07/30
 */
public interface SafeInventoryLockQueryService {

    /**
     * 根据ID查询安全库存锁定记录
     *
     * @param id 主键ID
     * @return 安全库存锁定记录
     */
    SafeInventoryLockRes findById(Long id);

    /**
     * 根据锁定编号查询安全库存锁定记录
     *
     * @param lockNo 锁定编号
     * @return 安全库存锁定记录
     */
    SafeInventoryLockRes findByLockNo(String lockNo);

    /**
     * 根据条件查询安全库存锁定记录列表
     *
     * @param query 查询条件
     * @return 安全库存锁定记录列表
     */
    List<SafeInventoryLockRes> queryList(SafeInventoryLockQuery query);

    /**
     * 分页查询安全库存锁定记录
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<SafeInventoryLockRes> queryPage(SafeInventoryLockQuery query);

    /**
     * 根据仓库和SKU查询锁定记录
     *
     * @param warehouseNo 仓库编码
     * @param sku SKU编码
     * @return 安全库存锁定记录列表
     */
    List<SafeInventoryLockRes> findByWarehouseAndSku(Integer warehouseNo, String sku);

    /**
     * 根据仓库、SKU和批次查询锁定记录
     *
     * @param warehouseNo 仓库编码
     * @param sku SKU编码
     * @param batchNo 批次号
     * @return 安全库存锁定记录列表
     */
    List<SafeInventoryLockRes> findByWarehouseSkuAndBatch(Integer warehouseNo, String sku, String batchNo);
}
