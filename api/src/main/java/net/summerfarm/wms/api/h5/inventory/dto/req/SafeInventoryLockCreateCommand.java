package net.summerfarm.wms.api.h5.inventory.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 安全库存锁定创建命令
 * @date 2025/07/30
 */
@Data
public class SafeInventoryLockCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @NotNull(message = "仓库编码不能为空")
    private Integer warehouseNo;

    /**
     * SKU编码
     */
    @NotBlank(message = "SKU编码不能为空")
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定数量
     */
    @NotNull(message = "锁定数量不能为空")
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    @NotNull(message = "锁定类型不能为空")
    private Integer lockType;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 租户ID
     */
    private Long tenantId;
}
