package net.summerfarm.wms.api.h5.inventory.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 安全库存锁定操作命令
 * @date 2025/07/30
 */
@Data
public class SafeInventoryLockOperationCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 锁定编号
     */
    @NotBlank(message = "锁定编号不能为空")
    private String lockNo;

    /**
     * 锁定数量（仅锁定操作需要）
     */
    private Integer lockQuantity;

    /**
     * 操作原因
     */
    private String reason;
}
