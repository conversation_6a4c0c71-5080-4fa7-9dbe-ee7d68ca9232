package net.summerfarm.wms.api.h5.inventory.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定查询条件
 * @date 2025/07/30
 */
@Data
public class SafeInventoryLockQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * SKU编码
     */
    private String sku;

    /**
     * SKU编码列表
     */
    private List<String> skuList;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次号列表
     */
    private List<String> batchNoList;

    /**
     * 生产日期开始
     */
    private LocalDate produceDateStart;

    /**
     * 生产日期结束
     */
    private LocalDate produceDateEnd;

    /**
     * 保质期开始
     */
    private LocalDate qualityDateStart;

    /**
     * 保质期结束
     */
    private LocalDate qualityDateEnd;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定编号
     */
    private String lockNo;

    /**
     * 锁定类型
     */
    private Integer lockType;

    /**
     * 锁定类型列表
     */
    private List<Integer> lockTypeList;

    /**
     * 锁定状态
     */
    private Integer lockStatus;

    /**
     * 锁定状态列表
     */
    private List<Integer> lockStatusList;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 分页页码
     */
    @NotNull(message = "分页页码不能为空")
    private Integer pageIndex;

    /**
     * 分页大小
     */
    @NotNull(message = "分页大小不能为空")
    private Integer pageSize;
}
