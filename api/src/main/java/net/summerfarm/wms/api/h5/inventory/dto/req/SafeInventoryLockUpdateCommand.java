package net.summerfarm.wms.api.h5.inventory.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 安全库存锁定更新命令
 * @date 2025/07/30
 */
@Data
public class SafeInventoryLockUpdateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 初始化数量
     */
    private Integer initQuantity;

    /**
     * 锁定数量
     */
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    private Integer lockType;

    /**
     * 锁定状态
     */
    private Integer lockStatus;

    /**
     * 锁定原因
     */
    private String lockReason;
}
