package net.summerfarm.wms.application.inventory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.annotation.OperatorAnnotation;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryLockCommandService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockCreateCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockOperationCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockUpdateCommand;
import net.summerfarm.wms.common.enums.SafeInventoryLockStatusEnum;
import net.summerfarm.wms.domain.inventory.SafeInventoryLockRepository;
import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryLock;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.util.UUIDUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 安全库存锁定命令服务实现
 * @date 2025/07/30
 */
@Slf4j
@Service
public class SafeInventoryLockCommandServiceImpl implements SafeInventoryLockCommandService {

    @Resource
    private SafeInventoryLockRepository safeInventoryLockRepository;

    @Override
    @OperatorAnnotation
    public Long createSafeInventoryLock(SafeInventoryLockCreateCommand createCommand) {
        if (null == createCommand) {
            throw new BizException("创建命令不能为空");
        }

        // 参数校验
        validateCreateCommand(createCommand);

        // 生成锁定编号
        String lockNo = generateLockNo();

        // 构建领域对象
        SafeInventoryLock safeInventoryLock = SafeInventoryLock.builder()
                .warehouseNo(createCommand.getWarehouseNo())
                .sku(createCommand.getSku())
                .batchNo(createCommand.getBatchNo())
                .produceDate(createCommand.getProduceDate())
                .qualityDate(createCommand.getQualityDate())
                .cabinetCode(createCommand.getCabinetCode())
                .lockNo(lockNo)
                .initQuantity(createCommand.getLockQuantity())
                .lockQuantity(createCommand.getLockQuantity())
                .lockType(createCommand.getLockType())
                .lockStatus(SafeInventoryLockStatusEnum.LOCKED.getCode())
                .lockReason(createCommand.getLockReason())
                .tenantId(createCommand.getTenantId())
                .version(1)
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        return safeInventoryLockRepository.create(safeInventoryLock);
    }

    @Override
    @OperatorAnnotation
    public Boolean updateSafeInventoryLock(SafeInventoryLockUpdateCommand updateCommand) {
        if (null == updateCommand || null == updateCommand.getId()) {
            throw new BizException("更新命令或ID不能为空");
        }

        // 查询原记录
        SafeInventoryLock existingLock = safeInventoryLockRepository.findById(updateCommand.getId());
        if (null == existingLock) {
            throw new BizException("安全库存锁定记录不存在");
        }

        // 更新字段
        if (StringUtils.isNotBlank(updateCommand.getBatchNo())) {
            existingLock.setBatchNo(updateCommand.getBatchNo());
        }
        if (null != updateCommand.getProduceDate()) {
            existingLock.setProduceDate(updateCommand.getProduceDate());
        }
        if (null != updateCommand.getQualityDate()) {
            existingLock.setQualityDate(updateCommand.getQualityDate());
        }
        if (StringUtils.isNotBlank(updateCommand.getCabinetCode())) {
            existingLock.setCabinetCode(updateCommand.getCabinetCode());
        }
        if (null != updateCommand.getInitQuantity()) {
            existingLock.setInitQuantity(updateCommand.getInitQuantity());
        }
        if (null != updateCommand.getLockQuantity()) {
            existingLock.setLockQuantity(updateCommand.getLockQuantity());
        }
        if (null != updateCommand.getLockType()) {
            existingLock.setLockType(updateCommand.getLockType());
        }
        if (null != updateCommand.getLockStatus()) {
            existingLock.setLockStatus(updateCommand.getLockStatus());
        }
        if (StringUtils.isNotBlank(updateCommand.getLockReason())) {
            existingLock.setLockReason(updateCommand.getLockReason());
        }
        existingLock.setUpdateTime(new Date());

        Integer result = safeInventoryLockRepository.update(existingLock);
        return result > 0;
    }

    @Override
    @OperatorAnnotation
    public Boolean deleteSafeInventoryLock(Long id) {
        if (null == id) {
            throw new BizException("ID不能为空");
        }

        // 查询记录是否存在
        SafeInventoryLock existingLock = safeInventoryLockRepository.findById(id);
        if (null == existingLock) {
            throw new BizException("安全库存锁定记录不存在");
        }

        // 检查是否可以删除（例如：只有释放状态的记录才能删除）
        if (SafeInventoryLockStatusEnum.LOCKED.getCode().equals(existingLock.getLockStatus())) {
            throw new BizException("锁定状态的记录不能删除，请先释放锁定");
        }

        // 逻辑删除：更新状态为释放
        existingLock.setLockStatus(SafeInventoryLockStatusEnum.RELEASED.getCode());
        existingLock.setUpdateTime(new Date());

        Integer result = safeInventoryLockRepository.update(existingLock);
        return result > 0;
    }

    @Override
    @OperatorAnnotation
    public Boolean increaseLockQuantity(SafeInventoryLockOperationCommand operationCommand) {
        if (null == operationCommand || StringUtils.isBlank(operationCommand.getLockNo())) {
            throw new BizException("操作命令或锁定编号不能为空");
        }

        if (null == operationCommand.getLockQuantity() || operationCommand.getLockQuantity() <= 0) {
            throw new BizException("锁定数量必须大于0");
        }

        // 查询锁定记录获取当前版本号
        SafeInventoryLock existingLock = safeInventoryLockRepository.findByLockNo(operationCommand.getLockNo());
        if (null == existingLock) {
            throw new BizException("锁定记录不存在");
        }

        // 检查是否可以增加锁定数量
        if (existingLock.getLockQuantity() + operationCommand.getLockQuantity() > existingLock.getInitQuantity()) {
            throw new BizException("锁定数量不能超过初始化数量");
        }

        // 执行增加锁定数量操作（乐观锁）
        Integer result = safeInventoryLockRepository.increaseLockQuantity(
                operationCommand.getLockNo(),
                operationCommand.getLockQuantity(),
                null, // updateOperator 通过 @OperatorAnnotation 自动注入
                existingLock.getVersion()
        );

        if (result <= 0) {
            throw new BizException("锁定失败，可能存在并发操作，请重试");
        }

        return true;
    }

    @Override
    @OperatorAnnotation
    public Boolean decreaseLockQuantity(SafeInventoryLockOperationCommand operationCommand) {
        if (null == operationCommand || StringUtils.isBlank(operationCommand.getLockNo())) {
            throw new BizException("操作命令或锁定编号不能为空");
        }

        if (null == operationCommand.getLockQuantity() || operationCommand.getLockQuantity() <= 0) {
            throw new BizException("减少数量必须大于0");
        }

        // 查询锁定记录获取当前版本号
        SafeInventoryLock existingLock = safeInventoryLockRepository.findByLockNo(operationCommand.getLockNo());
        if (null == existingLock) {
            throw new BizException("锁定记录不存在");
        }

        // 检查是否可以减少锁定数量
        if (existingLock.getLockQuantity() < operationCommand.getLockQuantity()) {
            throw new BizException("减少数量不能大于当前锁定数量");
        }

        // 执行减少锁定数量操作（乐观锁）
        Integer result = safeInventoryLockRepository.decreaseLockQuantity(
                operationCommand.getLockNo(),
                operationCommand.getLockQuantity(),
                null, // updateOperator 通过 @OperatorAnnotation 自动注入
                existingLock.getVersion()
        );

        if (result <= 0) {
            throw new BizException("减少锁定数量失败，可能存在并发操作，请重试");
        }

        return true;
    }

    @Override
    @OperatorAnnotation
    public Boolean releaseLock(SafeInventoryLockOperationCommand operationCommand) {
        if (null == operationCommand || StringUtils.isBlank(operationCommand.getLockNo())) {
            throw new BizException("操作命令或锁定编号不能为空");
        }

        // 查询锁定记录获取当前版本号
        SafeInventoryLock existingLock = safeInventoryLockRepository.findByLockNo(operationCommand.getLockNo());
        if (null == existingLock) {
            throw new BizException("锁定记录不存在");
        }

        // 检查状态
        if (SafeInventoryLockStatusEnum.RELEASED.getCode().equals(existingLock.getLockStatus())) {
            throw new BizException("该记录已经处于释放状态");
        }

        // 执行释放操作（乐观锁）
        Integer result = safeInventoryLockRepository.releaseLock(
                operationCommand.getLockNo(),
                null, // updateOperator 通过 @OperatorAnnotation 自动注入
                existingLock.getVersion()
        );

        if (result <= 0) {
            throw new BizException("释放失败，可能存在并发操作，请重试");
        }

        return true;
    }

    /**
     * 校验创建命令
     */
    private void validateCreateCommand(SafeInventoryLockCreateCommand createCommand) {
        if (null == createCommand.getWarehouseNo()) {
            throw new BizException("仓库编码不能为空");
        }
        if (StringUtils.isBlank(createCommand.getSku())) {
            throw new BizException("SKU编码不能为空");
        }
        if (null == createCommand.getInitQuantity() || createCommand.getInitQuantity() <= 0) {
            throw new BizException("初始化数量必须大于0");
        }
        if (null == createCommand.getLockQuantity() || createCommand.getLockQuantity() <= 0) {
            throw new BizException("锁定数量必须大于0");
        }
        if (createCommand.getLockQuantity() > createCommand.getInitQuantity()) {
            throw new BizException("锁定数量不能大于初始化数量");
        }
        if (null == createCommand.getLockType()) {
            throw new BizException("锁定类型不能为空");
        }
    }

    /**
     * 生成锁定编号
     */
    private String generateLockNo() {
        return "LOCK_" + UUIDUtil.getUUID32();
    }
}
