package net.summerfarm.wms.application.inventory;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryLockQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockQuery;
import net.summerfarm.wms.api.h5.inventory.dto.res.SafeInventoryLockRes;
import net.summerfarm.wms.application.inventory.converter.SafeInventoryLockConverter;
import net.summerfarm.wms.common.enums.SafeInventoryLockStatusEnum;
import net.summerfarm.wms.common.enums.SafeInventoryLockTypeEnum;
import net.summerfarm.wms.domain.inventory.SafeInventoryLockRepository;
import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryLock;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 安全库存锁定查询服务实现
 * @date 2025/07/30
 */
@Slf4j
@Service
public class SafeInventoryLockQueryServiceImpl implements SafeInventoryLockQueryService {

    @Resource
    private SafeInventoryLockRepository safeInventoryLockRepository;

    @Override
    public SafeInventoryLockRes findById(Long id) {
        if (null == id) {
            return null;
        }
        SafeInventoryLock safeInventoryLock = safeInventoryLockRepository.findById(id);
        return convertToRes(safeInventoryLock);
    }

    @Override
    public SafeInventoryLockRes findByLockNo(String lockNo) {
        if (StringUtils.isBlank(lockNo)) {
            return null;
        }
        SafeInventoryLock safeInventoryLock = safeInventoryLockRepository.findByLockNo(lockNo);
        return convertToRes(safeInventoryLock);
    }

    @Override
    public List<SafeInventoryLockRes> queryList(SafeInventoryLockQuery query) {
        if (null == query) {
            return Lists.newArrayList();
        }

        SafeInventoryLock condition = buildQueryCondition(query);
        List<SafeInventoryLock> safeInventoryLocks = safeInventoryLockRepository.list(condition);
        
        return safeInventoryLocks.stream()
                .map(this::convertToRes)
                .collect(Collectors.toList());
    }

    @Override
    public PageInfo<SafeInventoryLockRes> queryPage(SafeInventoryLockQuery query) {
        if (null == query || null == query.getPageIndex() || null == query.getPageSize()) {
            return new PageInfo<>(Lists.newArrayList());
        }

        SafeInventoryLock condition = buildQueryCondition(query);
        PageInfo<SafeInventoryLock> pageInfo = safeInventoryLockRepository.page(
                condition, query.getPageIndex(), query.getPageSize());

        PageInfo<SafeInventoryLockRes> result = new PageInfo<>();
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        
        List<SafeInventoryLockRes> resList = pageInfo.getList().stream()
                .map(this::convertToRes)
                .collect(Collectors.toList());
        result.setList(resList);
        
        return result;
    }

    @Override
    public List<SafeInventoryLockRes> findByWarehouseAndSku(Integer warehouseNo, String sku) {
        if (null == warehouseNo || StringUtils.isBlank(sku)) {
            return Lists.newArrayList();
        }

        List<SafeInventoryLock> safeInventoryLocks = safeInventoryLockRepository.findByWarehouseAndSku(warehouseNo, sku);
        return safeInventoryLocks.stream()
                .map(this::convertToRes)
                .collect(Collectors.toList());
    }

    @Override
    public List<SafeInventoryLockRes> findByWarehouseSkuAndBatch(Integer warehouseNo, String sku, String batchNo) {
        if (null == warehouseNo || StringUtils.isBlank(sku)) {
            return Lists.newArrayList();
        }

        List<SafeInventoryLock> safeInventoryLocks = safeInventoryLockRepository.findByWarehouseSkuAndBatch(warehouseNo, sku, batchNo);
        return safeInventoryLocks.stream()
                .map(this::convertToRes)
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private SafeInventoryLock buildQueryCondition(SafeInventoryLockQuery query) {
        SafeInventoryLock.SafeInventoryLockBuilder builder = SafeInventoryLock.builder();

        if (null != query.getId()) {
            builder.id(query.getId());
        }
        if (null != query.getWarehouseNo()) {
            builder.warehouseNo(query.getWarehouseNo());
        }
        if (StringUtils.isNotBlank(query.getSku())) {
            builder.sku(query.getSku());
        }
        if (StringUtils.isNotBlank(query.getBatchNo())) {
            builder.batchNo(query.getBatchNo());
        }
        if (StringUtils.isNotBlank(query.getCabinetCode())) {
            builder.cabinetCode(query.getCabinetCode());
        }
        if (StringUtils.isNotBlank(query.getLockNo())) {
            builder.lockNo(query.getLockNo());
        }
        if (null != query.getLockType()) {
            builder.lockType(query.getLockType());
        }
        if (null != query.getLockStatus()) {
            builder.lockStatus(query.getLockStatus());
        }
        if (StringUtils.isNotBlank(query.getCreateOperator())) {
            builder.createOperator(query.getCreateOperator());
        }
        if (null != query.getTenantId()) {
            builder.tenantId(query.getTenantId());
        }

        return builder.build();
    }

    /**
     * 转换为响应对象
     */
    private SafeInventoryLockRes convertToRes(SafeInventoryLock safeInventoryLock) {
        if (null == safeInventoryLock) {
            return null;
        }

        SafeInventoryLockRes res = SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLock);
        
        // 设置枚举描述
        if (null != res.getLockType()) {
            SafeInventoryLockTypeEnum typeEnum = SafeInventoryLockTypeEnum.getByCode(res.getLockType());
            if (null != typeEnum) {
                res.setLockTypeDesc(typeEnum.getDesc());
            }
        }
        
        if (null != res.getLockStatus()) {
            SafeInventoryLockStatusEnum statusEnum = SafeInventoryLockStatusEnum.getByCode(res.getLockStatus());
            if (null != statusEnum) {
                res.setLockStatusDesc(statusEnum.getDesc());
            }
        }

        return res;
    }
}
