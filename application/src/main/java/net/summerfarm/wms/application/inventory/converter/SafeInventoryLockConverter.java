package net.summerfarm.wms.application.inventory.converter;

import net.summerfarm.wms.api.h5.inventory.dto.res.SafeInventoryLockRes;
import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryLock;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定转换器
 * @date 2025/07/30
 */
@Mapper
public interface SafeInventoryLockConverter {

    SafeInventoryLockConverter INSTANCE = Mappers.getMapper(SafeInventoryLockConverter.class);

    /**
     * 领域对象转响应对象
     *
     * @param safeInventoryLock 领域对象
     * @return 响应对象
     */
    SafeInventoryLockRes convert(SafeInventoryLock safeInventoryLock);

    /**
     * 领域对象列表转响应对象列表
     *
     * @param safeInventoryLocks 领域对象列表
     * @return 响应对象列表
     */
    List<SafeInventoryLockRes> convertList(List<SafeInventoryLock> safeInventoryLocks);
}
