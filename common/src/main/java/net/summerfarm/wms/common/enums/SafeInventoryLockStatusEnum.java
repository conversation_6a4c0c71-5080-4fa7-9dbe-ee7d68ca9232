package net.summerfarm.wms.common.enums;

/**
 * <AUTHOR>
 * @description 安全库存锁定状态枚举
 * @date 2025/07/30
 */
public enum SafeInventoryLockStatusEnum {

    RELEASED(0, "释放"),
    LOCKED(1, "锁定"),
    ;

    private final Integer code;
    private final String desc;

    SafeInventoryLockStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SafeInventoryLockStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SafeInventoryLockStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
