package net.summerfarm.wms.common.enums;

/**
 * <AUTHOR>
 * @description 安全库存锁定类型枚举
 * @date 2025/07/30
 */
public enum SafeInventoryLockTypeEnum {

    QUALITY_LOCK(1, "质量锁定"),
    SAFETY_LOCK(2, "安全锁定"),
    MANUAL_LOCK(3, "人工锁定"),
    SYSTEM_LOCK(4, "系统锁定"),
    ;

    private final Integer code;
    private final String desc;

    SafeInventoryLockTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SafeInventoryLockTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SafeInventoryLockTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
