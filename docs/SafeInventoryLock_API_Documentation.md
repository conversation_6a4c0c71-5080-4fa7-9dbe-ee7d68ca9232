# 安全库存锁定功能 API 文档

## 概述

安全库存锁定功能用于管理仓库中的库存锁定操作，支持对特定SKU、批次的库存进行锁定和释放操作。

## 数据库表结构

```sql
CREATE TABLE `wms_safe_inventory_lock` (
	`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
	`update_time` datetime NULL on update CURRENT_TIMESTAMP COMMENT 'update time',
	`warehouse_no` int NOT NULL COMMENT '仓库编码',
	`sku` varchar(30) NOT NULL COMMENT 'sku',
	`batch_no` varchar(30) NULL COMMENT '批次号',
	`produce_date` date NULL COMMENT '生产日期',
	`quality_date` date NULL COMMENT '保质期',
	`cabinet_code` varchar(30) NULL COMMENT '库位编码',
	`lock_no` varchar(32) NOT NULL COMMENT '锁定编号',
	`init_quantity` int unsigned NOT NULL COMMENT '初始化数量',
	`lock_quantity` int unsigned NOT NULL COMMENT '锁定数量',
	`lock_type` int NOT NULL COMMENT '锁定类型',
	`lock_status` int NOT NULL DEFAULT 1 COMMENT '锁定状态，1：锁定 0：释放',
	`lock_reason` varchar(255) NULL COMMENT '锁定原因',
	`create_operator` varchar(255) NULL COMMENT '创建人',
	`update_operator` varchar(255) NULL COMMENT '更新人',
	`version` int NOT NULL DEFAULT 1 COMMENT '版本号（乐观锁）',
	PRIMARY KEY (`id`),
	UNIQUE KEY `uniq_lock_no` (`lock_no`),
	KEY `idx_warehouse_sku_batch` (`warehouse_no`,`sku`,`batch_no`),
	KEY `idx_warehouse_batch_sku` (`warehouse_no`,`batch_no`,`sku`),
	KEY `idx_warehouse_status` (`warehouse_no`,`lock_status`)
) DEFAULT CHARACTER SET=utf8 COMMENT='安全库存锁定表';
```

## 枚举定义

### 锁定类型 (SafeInventoryLockTypeEnum)
- `QUALITY_LOCK(1, "质量锁定")`
- `SAFETY_LOCK(2, "安全锁定")`
- `MANUAL_LOCK(3, "人工锁定")`
- `SYSTEM_LOCK(4, "系统锁定")`

### 锁定状态 (SafeInventoryLockStatusEnum)
- `RELEASED(0, "释放")`
- `LOCKED(1, "锁定")`

## API 接口

### 基础 CRUD 操作

#### 1. 创建安全库存锁定记录
- **URL**: `POST /safe-inventory-lock/create`
- **请求体**: `SafeInventoryLockCreateCommand`
- **响应**: `CommonResult<Long>` - 返回创建的记录ID

#### 2. 更新安全库存锁定记录
- **URL**: `POST /safe-inventory-lock/update`
- **请求体**: `SafeInventoryLockUpdateCommand`
- **响应**: `CommonResult<Boolean>`

#### 3. 删除安全库存锁定记录
- **URL**: `POST /safe-inventory-lock/delete/{id}`
- **路径参数**: `id` - 记录ID
- **响应**: `CommonResult<Boolean>`

### 查询操作

#### 4. 根据ID查询
- **URL**: `GET /safe-inventory-lock/query/{id}`
- **路径参数**: `id` - 记录ID
- **响应**: `CommonResult<SafeInventoryLockRes>`

#### 5. 根据锁定编号查询
- **URL**: `GET /safe-inventory-lock/query/lock-no/{lockNo}`
- **路径参数**: `lockNo` - 锁定编号
- **响应**: `CommonResult<SafeInventoryLockRes>`

#### 6. 条件查询列表
- **URL**: `POST /safe-inventory-lock/query/list`
- **请求体**: `SafeInventoryLockQuery`
- **响应**: `CommonResult<List<SafeInventoryLockRes>>`

#### 7. 分页查询
- **URL**: `POST /safe-inventory-lock/query/page`
- **请求体**: `SafeInventoryLockQuery`
- **响应**: `CommonResult<PageInfo<SafeInventoryLockRes>>`

#### 8. 根据仓库和SKU查询
- **URL**: `GET /safe-inventory-lock/query/warehouse/{warehouseNo}/sku/{sku}`
- **路径参数**: 
  - `warehouseNo` - 仓库编码
  - `sku` - SKU编码
- **响应**: `CommonResult<List<SafeInventoryLockRes>>`

#### 9. 根据仓库、SKU和批次查询
- **URL**: `GET /safe-inventory-lock/query/warehouse/{warehouseNo}/sku/{sku}/batch/{batchNo}`
- **路径参数**: 
  - `warehouseNo` - 仓库编码
  - `sku` - SKU编码
  - `batchNo` - 批次号
- **响应**: `CommonResult<List<SafeInventoryLockRes>>`

### 锁定操作

#### 10. 增加锁定数量
- **URL**: `POST /safe-inventory-lock/increase`
- **请求体**: `SafeInventoryLockOperationCommand`
- **响应**: `CommonResult<Boolean>`
- **说明**: 使用乐观锁机制增加指定锁定记录的锁定数量

#### 11. 减少锁定数量
- **URL**: `POST /safe-inventory-lock/decrease`
- **请求体**: `SafeInventoryLockOperationCommand`
- **响应**: `CommonResult<Boolean>`
- **说明**: 使用乐观锁机制减少指定锁定记录的锁定数量

#### 12. 释放锁定
- **URL**: `POST /safe-inventory-lock/release`
- **请求体**: `SafeInventoryLockOperationCommand`
- **响应**: `CommonResult<Boolean>`
- **说明**: 使用乐观锁机制将锁定状态设置为释放

## 请求/响应对象

### SafeInventoryLockCreateCommand
```json
{
  "warehouseNo": 1,
  "sku": "SKU001",
  "batchNo": "BATCH001",
  "produceDate": "2025-07-30",
  "qualityDate": "2025-08-30",
  "cabinetCode": "A001",
  "initQuantity": 100,
  "lockQuantity": 50,
  "lockType": 1,
  "lockReason": "质量问题",
  "tenantId": 1
}
```

### SafeInventoryLockUpdateCommand
```json
{
  "id": 1,
  "batchNo": "BATCH002",
  "lockQuantity": 60,
  "lockReason": "更新锁定原因"
}
```

### SafeInventoryLockQuery
```json
{
  "warehouseNo": 1,
  "sku": "SKU001",
  "lockStatus": 1,
  "pageIndex": 1,
  "pageSize": 10
}
```

### SafeInventoryLockOperationCommand
```json
{
  "lockNo": "LOCK_12345",
  "lockQuantity": 50,
  "reason": "操作原因"
}
```
**注意**:
- 对于增加锁定数量操作，`lockQuantity`表示要增加的数量
- 对于减少锁定数量操作，`lockQuantity`表示要减少的数量
- 对于释放锁定操作，`lockQuantity`字段可以忽略

### SafeInventoryLockRes
```json
{
  "id": 1,
  "createTime": "2025-07-30T10:00:00",
  "updateTime": "2025-07-30T10:00:00",
  "warehouseNo": 1,
  "sku": "SKU001",
  "batchNo": "BATCH001",
  "produceDate": "2025-07-30",
  "qualityDate": "2025-08-30",
  "cabinetCode": "A001",
  "lockNo": "LOCK_12345",
  "initQuantity": 100,
  "lockQuantity": 50,
  "lockType": 1,
  "lockTypeDesc": "质量锁定",
  "lockStatus": 1,
  "lockStatusDesc": "锁定",
  "lockReason": "质量问题",
  "createOperator": "admin",
  "updateOperator": "admin",
  "tenantId": 1,
  "version": 1
}
```

## 业务规则

1. **锁定编号唯一性**: 每个锁定记录都有唯一的锁定编号，格式为 `LOCK_` + UUID
2. **数量校验**:
   - 创建时：锁定数量不能大于初始化数量
   - 增加时：增加后的锁定数量不能超过初始化数量
   - 减少时：减少的数量不能大于当前锁定数量
3. **状态校验**:
   - 只有释放状态的记录才能删除
   - 已释放的记录不能重复释放
4. **乐观锁机制**:
   - 所有锁定数量变更操作都使用乐观锁防止并发冲突
   - 每次更新操作都会检查版本号，更新成功后版本号自动递增
   - 并发冲突时操作失败，需要重新获取最新数据后重试
5. **索引优化**: 提供了多个组合索引以支持高效查询

## 技术架构

本功能遵循DDD（领域驱动设计）架构模式：

- **Controller层**: `SafeInventoryLockController` - 处理HTTP请求
- **Application层**: `SafeInventoryLockCommandServiceImpl`, `SafeInventoryLockQueryServiceImpl` - 业务逻辑处理
- **Domain层**: `SafeInventoryLock`, `SafeInventoryLockRepository` - 领域模型和仓储接口
- **Infrastructure层**: `SafeInventoryLockRepositoryImpl`, `SafeInventoryLockDAO` - 数据访问实现

## 使用示例

### 创建锁定记录
```bash
curl -X POST http://localhost:8080/safe-inventory-lock/create \
  -H "Content-Type: application/json" \
  -d '{
    "warehouseNo": 1,
    "sku": "SKU001",
    "initQuantity": 100,
    "lockQuantity": 50,
    "lockType": 1,
    "lockReason": "质量检查"
  }'
```

### 分页查询
```bash
curl -X POST http://localhost:8080/safe-inventory-lock/query/page \
  -H "Content-Type: application/json" \
  -d '{
    "warehouseNo": 1,
    "pageIndex": 1,
    "pageSize": 10
  }'
```

### 增加锁定数量
```bash
curl -X POST http://localhost:8080/safe-inventory-lock/increase \
  -H "Content-Type: application/json" \
  -d '{
    "lockNo": "LOCK_12345",
    "lockQuantity": 30
  }'
```

### 减少锁定数量
```bash
curl -X POST http://localhost:8080/safe-inventory-lock/decrease \
  -H "Content-Type: application/json" \
  -d '{
    "lockNo": "LOCK_12345",
    "lockQuantity": 10
  }'
```

### 释放锁定
```bash
curl -X POST http://localhost:8080/safe-inventory-lock/release \
  -H "Content-Type: application/json" \
  -d '{
    "lockNo": "LOCK_12345"
  }'
```
