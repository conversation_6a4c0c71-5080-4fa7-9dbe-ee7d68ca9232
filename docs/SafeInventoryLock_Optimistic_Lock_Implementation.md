# 安全库存锁定功能 - 乐观锁实现说明

## 概述

根据需求，对安全库存锁定功能进行了重构，实现了基于乐观锁的增量操作，确保并发安全性。

## 主要变更

### 1. 数据库层面变更

#### 1.1 添加版本号字段
- 在 `wms_safe_inventory_lock` 表中添加 `version` 字段
- 类型：`int NOT NULL DEFAULT 1`
- 用途：乐观锁版本控制

#### 1.2 SQL操作变更
- **原来**：直接赋值操作 `SET lock_quantity = #{lockQuantity}`
- **现在**：增量操作 `SET lock_quantity = lock_quantity + #{incrementQuantity}`
- **乐观锁**：所有更新操作都包含 `WHERE version = #{version}` 条件
- **版本递增**：每次更新成功后 `version = version + 1`

### 2. 接口变更

#### 2.1 原接口
```java
// 旧接口 - 直接设置锁定数量
Boolean lockInventory(SafeInventoryLockOperationCommand operationCommand);
```

#### 2.2 新接口
```java
// 新接口 - 增量操作
Boolean increaseLockQuantity(SafeInventoryLockOperationCommand operationCommand); // 增加锁定数量
Boolean decreaseLockQuantity(SafeInventoryLockOperationCommand operationCommand); // 减少锁定数量
Boolean releaseLock(SafeInventoryLockOperationCommand operationCommand);         // 释放锁定
```

### 3. API端点变更

| 操作 | 原端点 | 新端点 | 说明 |
|------|--------|--------|------|
| 增加锁定数量 | `/lock` | `/increase` | 使用增量操作 |
| 减少锁定数量 | - | `/decrease` | 新增功能 |
| 释放锁定 | `/release` | `/release` | 保持不变，但增加乐观锁 |

### 4. 核心实现机制

#### 4.1 乐观锁SQL示例

**增加锁定数量**：
```sql
UPDATE wms_safe_inventory_lock
SET 
    lock_quantity = lock_quantity + #{incrementQuantity},
    lock_status = 1,
    update_time = now(),
    version = version + 1,
    update_operator = #{updateOperator}
WHERE lock_no = #{lockNo} 
AND version = #{version}
AND lock_quantity + #{incrementQuantity} >= 0
AND lock_quantity + #{incrementQuantity} <= init_quantity
```

**减少锁定数量**：
```sql
UPDATE wms_safe_inventory_lock
SET 
    lock_quantity = lock_quantity - #{decrementQuantity},
    update_time = now(),
    version = version + 1,
    update_operator = #{updateOperator}
WHERE lock_no = #{lockNo} 
AND version = #{version}
AND lock_quantity >= #{decrementQuantity}
```

**释放锁定**：
```sql
UPDATE wms_safe_inventory_lock
SET 
    lock_status = 0,
    update_time = now(),
    version = version + 1,
    update_operator = #{updateOperator}
WHERE lock_no = #{lockNo} 
AND version = #{version}
AND lock_status = 1
```

#### 4.2 并发控制流程

1. **查询当前记录**：获取当前的 `version` 值
2. **业务校验**：检查操作的合法性（数量限制等）
3. **乐观锁更新**：使用当前 `version` 执行更新操作
4. **结果检查**：
   - 更新成功（影响行数 > 0）：操作完成
   - 更新失败（影响行数 = 0）：发生并发冲突，抛出异常要求重试

### 5. 错误处理

#### 5.1 并发冲突处理
```java
Integer result = safeInventoryLockRepository.increaseLockQuantity(
    lockNo, incrementQuantity, updateOperator, version);

if (result <= 0) {
    throw new BizException("锁定失败，可能存在并发操作，请重试");
}
```

#### 5.2 业务规则校验
- **增加锁定数量**：检查增加后不超过初始化数量
- **减少锁定数量**：检查减少数量不大于当前锁定数量
- **释放锁定**：检查当前状态为锁定状态

### 6. 优势

#### 6.1 并发安全
- 使用乐观锁避免了悲观锁的性能问题
- 防止并发操作导致的数据不一致
- 在高并发场景下保证数据准确性

#### 6.2 操作精确性
- 增量操作避免了覆盖其他并发操作的结果
- 每次操作都基于当前最新状态进行计算
- 支持细粒度的数量调整

#### 6.3 可扩展性
- 版本号机制为未来的审计和历史追踪提供基础
- 清晰的操作语义便于业务理解和维护

### 7. 使用示例

#### 7.1 增加锁定数量
```bash
curl -X POST http://localhost:8080/safe-inventory-lock/increase \
  -H "Content-Type: application/json" \
  -d '{
    "lockNo": "LOCK_12345",
    "lockQuantity": 30
  }'
```

#### 7.2 减少锁定数量
```bash
curl -X POST http://localhost:8080/safe-inventory-lock/decrease \
  -H "Content-Type: application/json" \
  -d '{
    "lockNo": "LOCK_12345",
    "lockQuantity": 10
  }'
```

### 8. 注意事项

1. **重试机制**：客户端应该实现重试机制处理并发冲突
2. **版本号管理**：版本号由系统自动管理，不需要客户端传递
3. **原子性**：每个操作都是原子的，要么全部成功要么全部失败
4. **性能考虑**：乐观锁在低冲突场景下性能优异，高冲突场景可能需要额外的重试策略

### 9. 数据库迁移

执行以下SQL添加版本号字段：
```sql
ALTER TABLE `wms_safe_inventory_lock` 
ADD COLUMN `version` int NOT NULL DEFAULT 1 COMMENT '版本号（乐观锁）' AFTER `update_operator`;

UPDATE `wms_safe_inventory_lock` SET `version` = 1 WHERE `version` IS NULL OR `version` = 0;
```

### 10. 测试建议

1. **单元测试**：测试各种边界条件和异常情况
2. **并发测试**：模拟多线程同时操作同一记录
3. **压力测试**：验证高并发场景下的性能表现
4. **集成测试**：测试完整的业务流程

这个实现确保了在高并发环境下安全库存锁定操作的数据一致性和操作准确性。
