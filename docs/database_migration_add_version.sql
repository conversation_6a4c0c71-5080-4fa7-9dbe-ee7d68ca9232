-- 为安全库存锁定表添加版本号字段（乐观锁）
-- 执行时间：2025-07-30

-- 添加version字段
ALTER TABLE `wms_safe_inventory_lock` 
ADD COLUMN `version` int NOT NULL DEFAULT 1 COMMENT '版本号（乐观锁）' AFTER `update_operator`;

-- 为现有数据设置初始版本号
UPDATE `wms_safe_inventory_lock` SET `version` = 1 WHERE `version` IS NULL OR `version` = 0;

-- 验证字段添加成功
SELECT COUNT(*) as total_records, 
       COUNT(CASE WHEN version = 1 THEN 1 END) as version_1_records
FROM `wms_safe_inventory_lock`;

-- 显示表结构确认
DESCRIBE `wms_safe_inventory_lock`;
