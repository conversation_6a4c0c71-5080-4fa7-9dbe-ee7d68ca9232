package net.summerfarm.wms.domain.inventory;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryLock;

import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定仓储接口
 * @date 2025/07/30
 */
public interface SafeInventoryLockRepository {

    /**
     * 创建安全库存锁定记录
     *
     * @param safeInventoryLock 安全库存锁定对象
     * @return 主键ID
     */
    Long create(SafeInventoryLock safeInventoryLock);

    /**
     * 批量创建安全库存锁定记录
     *
     * @param safeInventoryLocks 安全库存锁定对象列表
     * @return 创建数量
     */
    Integer creates(List<SafeInventoryLock> safeInventoryLocks);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 安全库存锁定对象
     */
    SafeInventoryLock findById(Long id);

    /**
     * 根据ID列表查询
     *
     * @param ids 主键ID列表
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLock> findByIds(List<Long> ids);

    /**
     * 根据锁定编号查询
     *
     * @param lockNo 锁定编号
     * @return 安全库存锁定对象
     */
    SafeInventoryLock findByLockNo(String lockNo);

    /**
     * 更新安全库存锁定记录
     *
     * @param safeInventoryLock 安全库存锁定对象
     * @return 更新数量
     */
    Integer update(SafeInventoryLock safeInventoryLock);

    /**
     * 根据条件查询数量
     *
     * @param safeInventoryLock 查询条件
     * @return 数量
     */
    Long count(SafeInventoryLock safeInventoryLock);

    /**
     * 根据条件查询单个记录
     *
     * @param safeInventoryLock 查询条件
     * @return 安全库存锁定对象
     */
    SafeInventoryLock findOne(SafeInventoryLock safeInventoryLock);

    /**
     * 根据条件查询列表
     *
     * @param safeInventoryLock 查询条件
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLock> list(SafeInventoryLock safeInventoryLock);

    /**
     * 分页查询
     *
     * @param safeInventoryLock 查询条件
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    PageInfo<SafeInventoryLock> page(SafeInventoryLock safeInventoryLock, Integer pageIndex, Integer pageSize);

    /**
     * 根据仓库和SKU查询锁定记录
     *
     * @param warehouseNo 仓库编码
     * @param sku SKU编码
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLock> findByWarehouseAndSku(Integer warehouseNo, String sku);

    /**
     * 根据仓库、SKU和批次查询锁定记录
     *
     * @param warehouseNo 仓库编码
     * @param sku SKU编码
     * @param batchNo 批次号
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLock> findByWarehouseSkuAndBatch(Integer warehouseNo, String sku, String batchNo);

    /**
     * 锁定库存
     *
     * @param lockNo 锁定编号
     * @param lockQuantity 锁定数量
     * @param updateOperator 更新人
     * @return 更新数量
     */
    Integer lockInventory(String lockNo, Integer lockQuantity, String updateOperator);

    /**
     * 释放锁定
     *
     * @param lockNo 锁定编号
     * @param updateOperator 更新人
     * @return 更新数量
     */
    Integer releaseLock(String lockNo, String updateOperator);
}
