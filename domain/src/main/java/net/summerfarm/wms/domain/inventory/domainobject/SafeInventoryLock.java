package net.summerfarm.wms.domain.inventory.domainobject;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 安全库存锁定领域对象
 * @date 2025/07/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class SafeInventoryLock implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * SKU编码
     */
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate produceDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 锁定编号
     */
    private String lockNo;

    /**
     * 初始化数量
     */
    private Integer initQuantity;

    /**
     * 锁定数量
     */
    private Integer lockQuantity;

    /**
     * 锁定类型
     */
    private Integer lockType;

    /**
     * 锁定状态，1：锁定 0：释放
     */
    private Integer lockStatus;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 租户ID
     */
    private Long tenantId;
}
