package net.summerfarm.wms.domain.inventory.domainobject.enums;

import java.util.ArrayList;
import java.util.List;

public enum CabinetInventoryChangeTypeEnum {

    /**
     * 入库
     */
    INIT_IN(8, "期初入库"),
    PURCHASE_IN(11, "采购入库"),
//    TRANSFER_IN(16, "转换入库"),
    STOCK_TAKING_IN(15, "盘盈入库"),
    STORE_ALLOCATION_IN(10, "调拨入库"),
    AFTER_SALE_IN(19,"退货入库"),
    LACK_GOODS_APPROVED(20, "缺货入库"),
    RETURN_IN(13, "拒收入库"),
    ALLOCATION_ABNORMAL_IN(18, "调拨回库"),
    SKIP_STORE_IN(21, "越仓入库"),
    STOP_STORE_IN(22, "拦截入库"),
    OUT_MORE_IN(23, "多出入库"),
    OTHER_IN(24, "其他入库"),
    CROSS_WAREHOUSE_IN(25, "越库入库"),


    /**
     * 出库
     */
    SALE_OUT(51, "销售出库"),
//    TRANSFORM_OUT(55, "转换出库"),
    STOCK_TAKING_OUT(54, "盘亏出库"),
    STORE_ALLOCATION_OUT(50, "调拨出库"),
    DAMAGE_OUT(53, "货损出库"),
    DEMO_OUT(52, "出样出库"),
    PURCHASES_BACK_OUT(56, "采购退货出库"),
    RECOVER_OUT(57, "补货出库"),
    OWN_SALE_OUT(58, "销售自提出库"),
    ALLOCATION_DAMAGE_OUT_TASK(59, "调拨货损出库任务"),
    SKIP_STORE_OUT(60, "越仓出库"),

    DEMO_SELF_OUT(62, "自提出样出库"),

    CROSS_OUT(63, "越库出库"),
    TRANSFER_DAMAGE_OUT(64, "转换货损出库"),
    /**
     * 库内
     */

//    SHELF_ON(82,"库内上架"),


    TRANSFORM(55,"转换"),
    STOCKTAKING(3,"盘点"),
    PROCESSING_TASK(86,"加工"),
    MOVE_CABINET(43,"移库"),
    PICKING(87,"拣货"),

    INIT(81,"初始化"),

    MATERIAL_TASK_RECEIVE(96, "物料领用"),
    MATERIAL_TASK_RESTORE(97, "物料归还"),
    ;

    private final int type;
    private final String typeName;


    CabinetInventoryChangeTypeEnum(int type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getTypeName() {
        return typeName;
    }

    public int getType() {
        return type;
    }

    public static List<String> getAllTypeName(){
        List<String> result = new ArrayList<>();
        for (CabinetInventoryChangeTypeEnum value : CabinetInventoryChangeTypeEnum.values()) {
            result.add(value.getTypeName());
        }
        return result;
    }

    public static String getTypeName(int type) {
        for (CabinetInventoryChangeTypeEnum changeTypeEnum : CabinetInventoryChangeTypeEnum.values()) {
            if (changeTypeEnum.getType() == type) {
                return changeTypeEnum.getTypeName();
            }
        }
        return "";
    }

    public boolean equalTypeName(String name) {
        return this.getTypeName().equals(name);
    }
}
