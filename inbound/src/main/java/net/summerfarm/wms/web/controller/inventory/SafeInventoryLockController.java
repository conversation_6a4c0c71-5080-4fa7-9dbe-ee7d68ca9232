package net.summerfarm.wms.web.controller.inventory;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryLockCommandService;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryLockQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockCreateCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockOperationCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockQuery;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockUpdateCommand;
import net.summerfarm.wms.api.h5.inventory.dto.res.SafeInventoryLockRes;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定控制器
 * @date 2025/07/30
 */
@RestController
@RequestMapping("/safe-inventory-lock")
public class SafeInventoryLockController {

    @Resource
    private SafeInventoryLockCommandService safeInventoryLockCommandService;
    @Resource
    private SafeInventoryLockQueryService safeInventoryLockQueryService;

    /**
     * 创建安全库存锁定记录
     */
    @PostMapping(value = "/create")
    public CommonResult<Long> createSafeInventoryLock(@Valid @RequestBody SafeInventoryLockCreateCommand createCommand) {
        return CommonResult.ok(safeInventoryLockCommandService.createSafeInventoryLock(createCommand));
    }

    /**
     * 分页查询安全库存锁定记录
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<SafeInventoryLockRes>> queryPage(@Valid @RequestBody SafeInventoryLockQuery query) {
        return CommonResult.ok(safeInventoryLockQueryService.queryPage(query));
    }

    /**
     * 增加锁定数量
     */
    @PostMapping(value = "/increase")
    public CommonResult<Boolean> increaseLockQuantity(@Valid @RequestBody SafeInventoryLockOperationCommand operationCommand) {
        return CommonResult.ok(safeInventoryLockCommandService.increaseLockQuantity(operationCommand));
    }

    /**
     * 减少锁定数量
     */
    @PostMapping(value = "/decrease")
    public CommonResult<Boolean> decreaseLockQuantity(@Valid @RequestBody SafeInventoryLockOperationCommand operationCommand) {
        return CommonResult.ok(safeInventoryLockCommandService.decreaseLockQuantity(operationCommand));
    }

}
