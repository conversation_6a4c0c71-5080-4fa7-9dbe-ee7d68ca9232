package net.summerfarm.wms.web.controller.inventory;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryLockCommandService;
import net.summerfarm.wms.api.h5.inventory.SafeInventoryLockQueryService;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockCreateCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockOperationCommand;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockQuery;
import net.summerfarm.wms.api.h5.inventory.dto.req.SafeInventoryLockUpdateCommand;
import net.summerfarm.wms.api.h5.inventory.dto.res.SafeInventoryLockRes;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定控制器
 * @date 2025/07/30
 */
@RestController
@RequestMapping("/safe-inventory-lock")
public class SafeInventoryLockController {

    @Resource
    private SafeInventoryLockCommandService safeInventoryLockCommandService;
    @Resource
    private SafeInventoryLockQueryService safeInventoryLockQueryService;

    /**
     * 创建安全库存锁定记录
     */
    @PostMapping(value = "/create")
    public CommonResult<Long> createSafeInventoryLock(@Valid @RequestBody SafeInventoryLockCreateCommand createCommand) {
        return CommonResult.ok(safeInventoryLockCommandService.createSafeInventoryLock(createCommand));
    }

    /**
     * 更新安全库存锁定记录
     */
    @PostMapping(value = "/update")
    public CommonResult<Boolean> updateSafeInventoryLock(@Valid @RequestBody SafeInventoryLockUpdateCommand updateCommand) {
        return CommonResult.ok(safeInventoryLockCommandService.updateSafeInventoryLock(updateCommand));
    }

    /**
     * 删除安全库存锁定记录
     */
    @PostMapping(value = "/delete/{id}")
    public CommonResult<Boolean> deleteSafeInventoryLock(@PathVariable Long id) {
        return CommonResult.ok(safeInventoryLockCommandService.deleteSafeInventoryLock(id));
    }

    /**
     * 根据ID查询安全库存锁定记录
     */
    @GetMapping(value = "/query/{id}")
    public CommonResult<SafeInventoryLockRes> findById(@PathVariable Long id) {
        return CommonResult.ok(safeInventoryLockQueryService.findById(id));
    }

    /**
     * 根据锁定编号查询安全库存锁定记录
     */
    @GetMapping(value = "/query/lock-no/{lockNo}")
    public CommonResult<SafeInventoryLockRes> findByLockNo(@PathVariable String lockNo) {
        return CommonResult.ok(safeInventoryLockQueryService.findByLockNo(lockNo));
    }

    /**
     * 根据条件查询安全库存锁定记录列表
     */
    @PostMapping(value = "/query/list")
    public CommonResult<List<SafeInventoryLockRes>> queryList(@Valid @RequestBody SafeInventoryLockQuery query) {
        return CommonResult.ok(safeInventoryLockQueryService.queryList(query));
    }

    /**
     * 分页查询安全库存锁定记录
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<SafeInventoryLockRes>> queryPage(@Valid @RequestBody SafeInventoryLockQuery query) {
        return CommonResult.ok(safeInventoryLockQueryService.queryPage(query));
    }

    /**
     * 根据仓库和SKU查询锁定记录
     */
    @GetMapping(value = "/query/warehouse/{warehouseNo}/sku/{sku}")
    public CommonResult<List<SafeInventoryLockRes>> findByWarehouseAndSku(@PathVariable Integer warehouseNo, 
                                                                           @PathVariable String sku) {
        return CommonResult.ok(safeInventoryLockQueryService.findByWarehouseAndSku(warehouseNo, sku));
    }

    /**
     * 根据仓库、SKU和批次查询锁定记录
     */
    @GetMapping(value = "/query/warehouse/{warehouseNo}/sku/{sku}/batch/{batchNo}")
    public CommonResult<List<SafeInventoryLockRes>> findByWarehouseSkuAndBatch(@PathVariable Integer warehouseNo,
                                                                               @PathVariable String sku,
                                                                               @PathVariable String batchNo) {
        return CommonResult.ok(safeInventoryLockQueryService.findByWarehouseSkuAndBatch(warehouseNo, sku, batchNo));
    }

    /**
     * 锁定库存
     */
    @PostMapping(value = "/lock")
    public CommonResult<Boolean> lockInventory(@Valid @RequestBody SafeInventoryLockOperationCommand operationCommand) {
        return CommonResult.ok(safeInventoryLockCommandService.lockInventory(operationCommand));
    }

    /**
     * 释放锁定
     */
    @PostMapping(value = "/release")
    public CommonResult<Boolean> releaseLock(@Valid @RequestBody SafeInventoryLockOperationCommand operationCommand) {
        return CommonResult.ok(safeInventoryLockCommandService.releaseLock(operationCommand));
    }
}
