package net.summerfarm.wms.infrastructure.dao.inventory;

import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定DAO
 * @date 2025/07/30
 */
public interface SafeInventoryLockDAO {

    /**
     * 创建安全库存锁定记录
     *
     * @param safeInventoryLock 安全库存锁定对象
     * @return 主键ID
     */
    Long create(SafeInventoryLockDO safeInventoryLock);

    /**
     * 批量创建安全库存锁定记录
     *
     * @param safeInventoryLocks 安全库存锁定对象列表
     * @return 创建数量
     */
    Integer creates(List<SafeInventoryLockDO> safeInventoryLocks);

    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 安全库存锁定对象
     */
    SafeInventoryLockDO findById(Long id);

    /**
     * 根据ID列表查询
     *
     * @param ids 主键ID列表
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLockDO> findByIds(List<Long> ids);

    /**
     * 根据锁定编号查询
     *
     * @param lockNo 锁定编号
     * @return 安全库存锁定对象
     */
    SafeInventoryLockDO findByLockNo(String lockNo);

    /**
     * 更新安全库存锁定记录
     *
     * @param safeInventoryLock 安全库存锁定对象
     * @return 更新数量
     */
    Integer update(SafeInventoryLockDO safeInventoryLock);

    /**
     * 根据条件查询数量
     *
     * @param safeInventoryLock 查询条件
     * @return 数量
     */
    Long count(SafeInventoryLockDO safeInventoryLock);

    /**
     * 根据条件查询单个记录
     *
     * @param safeInventoryLock 查询条件
     * @return 安全库存锁定对象
     */
    SafeInventoryLockDO findOne(SafeInventoryLockDO safeInventoryLock);

    /**
     * 根据条件查询列表
     *
     * @param safeInventoryLock 查询条件
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLockDO> list(SafeInventoryLockDO safeInventoryLock);

    /**
     * 根据仓库和SKU查询锁定记录
     *
     * @param warehouseNo 仓库编码
     * @param sku SKU编码
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLockDO> findByWarehouseAndSku(@Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku);

    /**
     * 根据仓库、SKU和批次查询锁定记录
     *
     * @param warehouseNo 仓库编码
     * @param sku SKU编码
     * @param batchNo 批次号
     * @return 安全库存锁定对象列表
     */
    List<SafeInventoryLockDO> findByWarehouseSkuAndBatch(@Param("warehouseNo") Integer warehouseNo, 
                                                          @Param("sku") String sku, 
                                                          @Param("batchNo") String batchNo);

    /**
     * 锁定库存
     *
     * @param lockNo 锁定编号
     * @param lockQuantity 锁定数量
     * @param updateOperator 更新人
     * @return 更新数量
     */
    Integer lockInventory(@Param("lockNo") String lockNo, 
                         @Param("lockQuantity") Integer lockQuantity, 
                         @Param("updateOperator") String updateOperator);

    /**
     * 释放锁定
     *
     * @param lockNo 锁定编号
     * @param updateOperator 更新人
     * @return 更新数量
     */
    Integer releaseLock(@Param("lockNo") String lockNo, @Param("updateOperator") String updateOperator);
}
