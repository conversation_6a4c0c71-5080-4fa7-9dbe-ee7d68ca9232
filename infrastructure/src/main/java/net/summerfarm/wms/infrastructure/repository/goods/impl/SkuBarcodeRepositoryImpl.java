package net.summerfarm.wms.infrastructure.repository.goods.impl;

import com.google.common.collect.Lists;
import net.summerfarm.wms.common.enums.SkuBarcodeStatusEnum;
import net.summerfarm.wms.domain.sku.SkuBarcodeRepository;
import net.summerfarm.wms.domain.sku.domainobject.SkuBarcode;
import net.summerfarm.wms.infrastructure.dao.base.dataobject.SortDO;
import net.summerfarm.wms.infrastructure.dao.goods.SkuBarcodeDAO;
import net.summerfarm.wms.infrastructure.dao.goods.dataobject.SkuBarcodeDO;
import net.summerfarm.wms.infrastructure.repository.goods.converter.SkuBarcodeConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description sku条码Repository
 * @date 2023/4/28
 */
@Component
public class SkuBarcodeRepositoryImpl implements SkuBarcodeRepository {

    @Resource
    private SkuBarcodeDAO skuBarcodeDAO;

    /**
     * create
     *
     * @param skuBarcode * @return
     */
    @Override
    public Long create(SkuBarcode skuBarcode) {
        if (null == skuBarcode) {
            return 0L;
        }
        SkuBarcodeDO skuBarcodeDO = SkuBarcodeConverter.INSTANCE.convert(skuBarcode);
        return skuBarcodeDAO.create(skuBarcodeDO);
    }

    /**
     * batch create
     *
     * @param skuBarcodes
     * @return
     */
    @Override
    public Integer creates(List<SkuBarcode> skuBarcodes) {
        if (CollectionUtils.isEmpty(skuBarcodes)) {
            return 0;
        }
        List<SkuBarcodeDO> skuBarcodeList = skuBarcodes.stream().map(SkuBarcodeConverter.INSTANCE::convert).collect(Collectors.toList());
        return skuBarcodeDAO.creates(skuBarcodeList);
    }

    /**
     * update
     *
     * @param skuBarcode
     * @return
     */
    @Override
    public Boolean update(SkuBarcode skuBarcode) {
        if (null == skuBarcode) {
            return false;
        }
        return skuBarcodeDAO.update(SkuBarcodeConverter.INSTANCE.convert(skuBarcode)) > 0;
    }

    /**
     * updateStatusBySku
     *
     * @param skuBarcode
     * @return
     */
    @Override
    public Boolean updateStatusBySku(SkuBarcode skuBarcode) {
        if (null == skuBarcode) {
            return false;
        }
        return skuBarcodeDAO.updateStatusBySku(SkuBarcodeConverter.INSTANCE.convert(skuBarcode)) > 0;
    }

    /**
     * findById
     *
     * @param id
     * @return
     */
    @Override
    public SkuBarcode findById(Long id) {
        if (null == id) {
            return null;
        }
        SkuBarcodeDO skuBarcodeDO = skuBarcodeDAO.findById(id);
        if (null == skuBarcodeDO) {
            return null;
        }
        return SkuBarcodeConverter.INSTANCE.convert(skuBarcodeDO);
    }

    /**
     * findByIds
     *
     * @param ids
     * @return
     */
    @Override
    public List<SkuBarcode> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<SkuBarcodeDO> skuBarcodeDOList = skuBarcodeDAO.findByIds(ids);
        if (CollectionUtils.isEmpty(skuBarcodeDOList)) {
            return new ArrayList<>();
        }
        return skuBarcodeDOList.stream().map(SkuBarcodeConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    /**
     * count
     *
     * @param skuBarcode
     * @return
     */
    @Override
    public Long count(SkuBarcode skuBarcode) {
        if (null == skuBarcode) {
            return 0L;
        }
        return skuBarcodeDAO.count(SkuBarcodeConverter.INSTANCE.convert(skuBarcode));
    }

    /**
     * findOne
     *
     * @param skuBarcode
     * @return
     */
    @Override
    public SkuBarcode findOne(SkuBarcode skuBarcode) {
        if (null == skuBarcode) {
            return null;
        }
        SkuBarcodeDO skuBarcodeDO = skuBarcodeDAO.findOne(SkuBarcodeConverter.INSTANCE.convert(skuBarcode));
        if (null == skuBarcodeDO) {
            return null;
        }
        return SkuBarcodeConverter.INSTANCE.convert(skuBarcodeDO);
    }

    /**
     * list
     *
     * @param skuBarcode
     * @return
     */
    @Override
    public List<SkuBarcode> list(SkuBarcode skuBarcode) {
        if (null == skuBarcode) {
            return new ArrayList<>();
        }
        SkuBarcodeDO skuBarcodeDO = SkuBarcodeConverter.INSTANCE.convert(skuBarcode);
        skuBarcodeDO.getSorts().add(new SortDO("update_time", SortDO.SortType.ASC.getName()));
        List<SkuBarcodeDO> skuBarcodeDOList = skuBarcodeDAO.list(skuBarcodeDO);
        if (CollectionUtils.isEmpty(skuBarcodeDOList)) {
            return new ArrayList<>();
        }
        return skuBarcodeDOList.stream().map(SkuBarcodeConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public List<SkuBarcode> listBySkus(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)){
            return Lists.newArrayList();
        }
        List<SkuBarcodeDO> skuBarcodeDOList = skuBarcodeDAO.listBySkus(skus);
        return skuBarcodeDOList.stream().map(SkuBarcodeConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public List<SkuBarcode> listBySku(String sku) {
        if (StringUtils.isBlank(sku)){
            return Lists.newArrayList();
        }
        List<SkuBarcodeDO> skuBarcodeDOList = skuBarcodeDAO.list(SkuBarcodeDO.builder().
                sku(sku)
                .status(SkuBarcodeStatusEnum.OPEN.getCode())
                .build());
        return skuBarcodeDOList.stream().map(SkuBarcodeConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    /**
     * 批量更新sku的状态内容
     *
     * @param skus   sku编码列表
     * @param status 状态值
     * @return 返回更新的行数
     */
    @Override
    public Boolean updateStatusBySkuList(List<String> skus, Integer status, Long tenantId, String updateOperator) {
        if (CollectionUtils.isEmpty(skus) || Objects.isNull(status) || Objects.isNull(tenantId)) {
            return Boolean.FALSE;
        }
        return skuBarcodeDAO.updateStatusBySkuList(skus, status, tenantId, updateOperator) > 0;
    }
}
