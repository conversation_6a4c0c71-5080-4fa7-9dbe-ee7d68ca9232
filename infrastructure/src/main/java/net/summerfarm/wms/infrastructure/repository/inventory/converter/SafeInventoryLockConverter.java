package net.summerfarm.wms.infrastructure.repository.inventory.converter;

import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryLock;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description 安全库存锁定转换器
 * @date 2025/07/30
 */
@Mapper
public interface SafeInventoryLockConverter {

    SafeInventoryLockConverter INSTANCE = Mappers.getMapper(SafeInventoryLockConverter.class);

    /**
     * 领域对象转数据对象
     *
     * @param safeInventoryLock 领域对象
     * @return 数据对象
     */
    SafeInventoryLockDO convert(SafeInventoryLock safeInventoryLock);

    /**
     * 数据对象转领域对象
     *
     * @param safeInventoryLockDO 数据对象
     * @return 领域对象
     */
    SafeInventoryLock convert(SafeInventoryLockDO safeInventoryLockDO);

    /**
     * 领域对象列表转数据对象列表
     *
     * @param safeInventoryLocks 领域对象列表
     * @return 数据对象列表
     */
    List<SafeInventoryLockDO> convertList(List<SafeInventoryLock> safeInventoryLocks);

    /**
     * 数据对象列表转领域对象列表
     *
     * @param safeInventoryLockDOs 数据对象列表
     * @return 领域对象列表
     */
    List<SafeInventoryLock> convertDOList(List<SafeInventoryLockDO> safeInventoryLockDOs);
}
