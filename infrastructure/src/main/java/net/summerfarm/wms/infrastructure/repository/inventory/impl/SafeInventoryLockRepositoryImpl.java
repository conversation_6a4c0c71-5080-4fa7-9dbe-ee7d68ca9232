package net.summerfarm.wms.infrastructure.repository.inventory.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.wms.common.enums.SafeInventoryLockStatusEnum;
import net.summerfarm.wms.domain.inventory.SafeInventoryLockRepository;
import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryLock;
import net.summerfarm.wms.infrastructure.dao.base.dataobject.SortDO;
import net.summerfarm.wms.infrastructure.dao.inventory.SafeInventoryLockDAO;
import net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO;
import net.summerfarm.wms.infrastructure.repository.inventory.converter.SafeInventoryLockConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 安全库存锁定仓储实现
 * @date 2025/07/30
 */
@Component
public class SafeInventoryLockRepositoryImpl implements SafeInventoryLockRepository {

    @Resource
    private SafeInventoryLockDAO safeInventoryLockDAO;

    @Override
    public Long create(SafeInventoryLock safeInventoryLock) {
        if (null == safeInventoryLock) {
            return 0L;
        }
        SafeInventoryLockDO safeInventoryLockDO = SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLock);
        return safeInventoryLockDAO.create(safeInventoryLockDO);
    }

    @Override
    public Integer creates(List<SafeInventoryLock> safeInventoryLocks) {
        if (CollectionUtils.isEmpty(safeInventoryLocks)) {
            return 0;
        }
        List<SafeInventoryLockDO> safeInventoryLockDOs = SafeInventoryLockConverter.INSTANCE.convertList(safeInventoryLocks);
        return safeInventoryLockDAO.creates(safeInventoryLockDOs);
    }

    @Override
    public SafeInventoryLock findById(Long id) {
        if (null == id) {
            return null;
        }
        SafeInventoryLockDO safeInventoryLockDO = safeInventoryLockDAO.findById(id);
        return SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLockDO);
    }

    @Override
    public List<SafeInventoryLock> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<SafeInventoryLockDO> safeInventoryLockDOs = safeInventoryLockDAO.findByIds(ids);
        return SafeInventoryLockConverter.INSTANCE.convertDOList(safeInventoryLockDOs);
    }

    @Override
    public SafeInventoryLock findByLockNo(String lockNo) {
        if (StringUtils.isBlank(lockNo)) {
            return null;
        }
        SafeInventoryLockDO safeInventoryLockDO = safeInventoryLockDAO.findByLockNo(lockNo);
        return SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLockDO);
    }

    @Override
    public Integer update(SafeInventoryLock safeInventoryLock) {
        if (null == safeInventoryLock || null == safeInventoryLock.getId()) {
            return 0;
        }
        SafeInventoryLockDO safeInventoryLockDO = SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLock);
        return safeInventoryLockDAO.update(safeInventoryLockDO);
    }

    @Override
    public Long count(SafeInventoryLock safeInventoryLock) {
        if (null == safeInventoryLock) {
            return 0L;
        }
        SafeInventoryLockDO safeInventoryLockDO = SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLock);
        return safeInventoryLockDAO.count(safeInventoryLockDO);
    }

    @Override
    public SafeInventoryLock findOne(SafeInventoryLock safeInventoryLock) {
        if (null == safeInventoryLock) {
            return null;
        }
        SafeInventoryLockDO safeInventoryLockDO = SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLock);
        SafeInventoryLockDO result = safeInventoryLockDAO.findOne(safeInventoryLockDO);
        return SafeInventoryLockConverter.INSTANCE.convert(result);
    }

    @Override
    public List<SafeInventoryLock> list(SafeInventoryLock safeInventoryLock) {
        if (null == safeInventoryLock) {
            return Lists.newArrayList();
        }
        SafeInventoryLockDO safeInventoryLockDO = SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLock);
        List<SafeInventoryLockDO> safeInventoryLockDOs = safeInventoryLockDAO.list(safeInventoryLockDO);
        return SafeInventoryLockConverter.INSTANCE.convertDOList(safeInventoryLockDOs);
    }

    @Override
    public PageInfo<SafeInventoryLock> page(SafeInventoryLock safeInventoryLock, Integer pageIndex, Integer pageSize) {
        if (null == safeInventoryLock || null == pageIndex || null == pageSize) {
            return new PageInfo<>(Lists.newArrayList());
        }
        SafeInventoryLockDO safeInventoryLockDO = SafeInventoryLockConverter.INSTANCE.convert(safeInventoryLock);
        safeInventoryLockDO.setSorts(Arrays.asList(new SortDO("create_time", SortDO.SortType.DESC.getName())));
        
        PageInfo<SafeInventoryLockDO> pageInfo = PageHelper.startPage(pageIndex, pageSize)
                .doSelectPageInfo(() -> safeInventoryLockDAO.list(safeInventoryLockDO));
        
        PageInfo<SafeInventoryLock> result = new PageInfo<>();
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setList(SafeInventoryLockConverter.INSTANCE.convertDOList(pageInfo.getList()));
        return result;
    }

    @Override
    public List<SafeInventoryLock> findByWarehouseAndSku(Integer warehouseNo, String sku) {
        if (null == warehouseNo || StringUtils.isBlank(sku)) {
            return Lists.newArrayList();
        }
        List<SafeInventoryLockDO> safeInventoryLockDOs = safeInventoryLockDAO.findByWarehouseAndSku(warehouseNo, sku);
        return SafeInventoryLockConverter.INSTANCE.convertDOList(safeInventoryLockDOs);
    }

    @Override
    public List<SafeInventoryLock> findByWarehouseSkuAndBatch(Integer warehouseNo, String sku, String batchNo) {
        if (null == warehouseNo || StringUtils.isBlank(sku)) {
            return Lists.newArrayList();
        }
        List<SafeInventoryLockDO> safeInventoryLockDOs = safeInventoryLockDAO.findByWarehouseSkuAndBatch(warehouseNo, sku, batchNo);
        return SafeInventoryLockConverter.INSTANCE.convertDOList(safeInventoryLockDOs);
    }

    @Override
    public Integer lockInventory(String lockNo, Integer lockQuantity, String updateOperator) {
        if (StringUtils.isBlank(lockNo) || null == lockQuantity || lockQuantity <= 0) {
            return 0;
        }
        return safeInventoryLockDAO.lockInventory(lockNo, lockQuantity, updateOperator);
    }

    @Override
    public Integer releaseLock(String lockNo, String updateOperator) {
        if (StringUtils.isBlank(lockNo)) {
            return 0;
        }
        return safeInventoryLockDAO.releaseLock(lockNo, updateOperator);
    }
}
