<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.SafeInventoryLockDAO">
    <resultMap id="SafeInventoryLockMap" type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="warehouseNo" column="warehouse_no"/>
        <result property="sku" column="sku"/>
        <result property="batchNo" column="batch_no"/>
        <result property="produceDate" column="produce_date"/>
        <result property="qualityDate" column="quality_date"/>
        <result property="cabinetCode" column="cabinet_code"/>
        <result property="lockNo" column="lock_no"/>
        <result property="initQuantity" column="init_quantity"/>
        <result property="lockQuantity" column="lock_quantity"/>
        <result property="lockType" column="lock_type"/>
        <result property="lockStatus" column="lock_status"/>
        <result property="lockReason" column="lock_reason"/>
        <result property="createOperator" column="create_operator"/>
        <result property="updateOperator" column="update_operator"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="version" column="version"/>
    </resultMap>

    <sql id="table_name">
        wms_safe_inventory_lock
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `create_time`, `update_time`, `warehouse_no`, `sku`, `batch_no`, `produce_date`, `quality_date`,
        `cabinet_code`, `lock_no`, `init_quantity`, `lock_quantity`, `lock_type`, `lock_status`,
        `lock_reason`, `create_operator`, `update_operator`, `tenant_id`, `version`
    </sql>

    <sql id="query">
        <where>
            <if test="id != null">AND `id` = #{id}</if>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
            <if test="warehouseNo != null">AND `warehouse_no` = #{warehouseNo}</if>
            <if test="sku != null">AND `sku` = #{sku}</if>
            <if test="batchNo != null">AND `batch_no` = #{batchNo}</if>
            <if test="produceDate != null">AND `produce_date` = #{produceDate}</if>
            <if test="qualityDate != null">AND `quality_date` = #{qualityDate}</if>
            <if test="cabinetCode != null">AND `cabinet_code` = #{cabinetCode}</if>
            <if test="lockNo != null">AND `lock_no` = #{lockNo}</if>
            <if test="initQuantity != null">AND `init_quantity` = #{initQuantity}</if>
            <if test="lockQuantity != null">AND `lock_quantity` = #{lockQuantity}</if>
            <if test="lockType != null">AND `lock_type` = #{lockType}</if>
            <if test="lockStatus != null">AND `lock_status` = #{lockStatus}</if>
            <if test="lockReason != null">AND `lock_reason` = #{lockReason}</if>
            <if test="createOperator != null">AND `create_operator` = #{createOperator}</if>
            <if test="updateOperator != null">AND `update_operator` = #{updateOperator}</if>
            <if test="tenantId != null">AND `tenant_id` = #{tenantId}</if>
            <if test="version != null">AND `version` = #{version}</if>
        </where>
    </sql>

    <sql id="orderByQuery">
        <if test="sorts != null and sorts.size() > 0">
            ORDER BY
            <foreach collection="sorts" item="i" index="index" separator=",">
                ${i.columnName} ${i.sortType}
            </foreach>
        </if>
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="warehouseNo != null">`warehouse_no`,</if>
            <if test="sku != null">`sku`,</if>
            <if test="batchNo != null">`batch_no`,</if>
            <if test="produceDate != null">`produce_date`,</if>
            <if test="qualityDate != null">`quality_date`,</if>
            <if test="cabinetCode != null">`cabinet_code`,</if>
            <if test="lockNo != null">`lock_no`,</if>
            <if test="initQuantity != null">`init_quantity`,</if>
            <if test="lockQuantity != null">`lock_quantity`,</if>
            <if test="lockType != null">`lock_type`,</if>
            <if test="lockStatus != null">`lock_status`,</if>
            <if test="lockReason != null">`lock_reason`,</if>
            <if test="createOperator != null">`create_operator`,</if>
            <if test="updateOperator != null">`update_operator`,</if>
            <if test="tenantId != null">`tenant_id`,</if>
            <if test="version != null">`version`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="warehouseNo != null">#{warehouseNo},</if>
            <if test="sku != null">#{sku},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="produceDate != null">#{produceDate},</if>
            <if test="qualityDate != null">#{qualityDate},</if>
            <if test="cabinetCode != null">#{cabinetCode},</if>
            <if test="lockNo != null">#{lockNo},</if>
            <if test="initQuantity != null">#{initQuantity},</if>
            <if test="lockQuantity != null">#{lockQuantity},</if>
            <if test="lockType != null">#{lockType},</if>
            <if test="lockStatus != null">#{lockStatus},</if>
            <if test="lockReason != null">#{lockReason},</if>
            <if test="createOperator != null">#{createOperator},</if>
            <if test="updateOperator != null">#{updateOperator},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="version != null">#{version},</if>
        </trim>
    </insert>

    <insert id="creates" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.createTime}, #{i.updateTime}, #{i.warehouseNo}, #{i.sku}, #{i.batchNo}, #{i.produceDate},
            #{i.qualityDate}, #{i.cabinetCode}, #{i.lockNo}, #{i.initQuantity}, #{i.lockQuantity},
            #{i.lockType}, #{i.lockStatus}, #{i.lockReason}, #{i.createOperator}, #{i.updateOperator}, #{i.tenantId}, #{i.version})
        </foreach>
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            <if test="warehouseNo != null">`warehouse_no` = #{warehouseNo},</if>
            <if test="sku != null">`sku` = #{sku},</if>
            <if test="batchNo != null">`batch_no` = #{batchNo},</if>
            <if test="produceDate != null">`produce_date` = #{produceDate},</if>
            <if test="qualityDate != null">`quality_date` = #{qualityDate},</if>
            <if test="cabinetCode != null">`cabinet_code` = #{cabinetCode},</if>
            <if test="lockNo != null">`lock_no` = #{lockNo},</if>
            <if test="initQuantity != null">`init_quantity` = #{initQuantity},</if>
            <if test="lockQuantity != null">`lock_quantity` = #{lockQuantity},</if>
            <if test="lockType != null">`lock_type` = #{lockType},</if>
            <if test="lockStatus != null">`lock_status` = #{lockStatus},</if>
            <if test="lockReason != null">`lock_reason` = #{lockReason},</if>
            <if test="createOperator != null">`create_operator` = #{createOperator},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            <if test="tenantId != null">`tenant_id` = #{tenantId},</if>
            <if test="version != null">`version` = #{version},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="SafeInventoryLockMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="SafeInventoryLockMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="findByLockNo" parameterType="java.lang.String" resultMap="SafeInventoryLockMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE lock_no = #{lockNo} LIMIT 1
    </select>

    <select id="count" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO"
            resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO"
            resultMap="SafeInventoryLockMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryLockDO"
            resultMap="SafeInventoryLockMap">
        /*FORCE_MASTER*/  SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    <select id="findByWarehouseAndSku" resultMap="SafeInventoryLockMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE warehouse_no = #{warehouseNo} AND sku = #{sku}
        ORDER BY create_time DESC
    </select>

    <select id="findByWarehouseSkuAndBatch" resultMap="SafeInventoryLockMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE warehouse_no = #{warehouseNo} AND sku = #{sku}
        <if test="batchNo != null and batchNo != ''">
            AND batch_no = #{batchNo}
        </if>
        ORDER BY create_time DESC
    </select>

    <update id="increaseLockQuantity">
        UPDATE
        <include refid="table_name"/>
        SET
            lock_quantity = lock_quantity + #{incrementQuantity},
            lock_status = 1,
            update_time = now(),
            version = version + 1
            <if test="updateOperator != null">, update_operator = #{updateOperator}</if>
        WHERE lock_no = #{lockNo}
        AND version = #{version}
        AND lock_quantity + #{incrementQuantity} >= 0
        AND lock_quantity + #{incrementQuantity} <= init_quantity
    </update>

    <update id="decreaseLockQuantity">
        UPDATE
        <include refid="table_name"/>
        SET
            lock_quantity = lock_quantity - #{decrementQuantity},
            update_time = now(),
            version = version + 1
            <if test="updateOperator != null">, update_operator = #{updateOperator}</if>
        WHERE lock_no = #{lockNo}
        AND version = #{version}
        AND lock_quantity >= #{decrementQuantity}
    </update>

    <update id="releaseLock">
        UPDATE
        <include refid="table_name"/>
        SET
            lock_status = 0,
            update_time = now(),
            version = version + 1
            <if test="updateOperator != null">, update_operator = #{updateOperator}</if>
        WHERE lock_no = #{lockNo}
        AND version = #{version}
        AND lock_status = 1
    </update>
</mapper>
